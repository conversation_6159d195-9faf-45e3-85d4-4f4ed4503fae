module "eks_prd" {
  source  = "terraform-aws-modules/eks/aws"
  version = "~> 20.0"

  cluster_name    = "eks-prd"
  cluster_version = "1.31"

  bootstrap_self_managed_addons = false
  cluster_addons = {
    coredns                = {}
    eks-pod-identity-agent = {}
    kube-proxy             = {}
    vpc-cni                = {}
  }

  # Optional
  cluster_endpoint_public_access = true

  # Optional: Adds the current caller identity as an administrator via cluster access entry
  enable_cluster_creator_admin_permissions = true

  vpc_id                    = aws_vpc.vpc_workload.id
  subnet_ids                = [aws_subnet.subnet_private_k8s_prd_a.id, aws_subnet.subnet_private_k8s_prd_b.id]
  control_plane_subnet_ids  = [aws_subnet.subnet_private_k8s_prd_cluster_a.id, aws_subnet.subnet_private_k8s_prd_cluster_b.id]
  cluster_service_ipv4_cidr = "**********/23"

  # EKS Managed Node Group(s)
  # eks_managed_node_group_defaults = {
  #   instance_types = ["m6i.large", "m5.large", "m5n.large", "m5zn.large"]
  # }

  eks_managed_node_groups = {
    node-pool-lab = {
      # Starting on 1.30, AL2023 is the default AMI type for EKS managed node groups
      ami_type       = "AL2023_x86_64_STANDARD"
      instance_types = ["t2.medium"]

      min_size     = 0
      max_size     = 2
      desired_size = 2
    }
  }

  tags = {
    env       = "prd"
    terraform = "true"
  }
}