# VPC
resource "aws_vpc" "vpc_workload" {
  cidr_block = "10.11.0.0/16"
  tags = {
    Name = "vpc-workload"
  }
}

# Subnets
# subnet-public-prd	*********	/24
# subnet-private-vm-prd	**********	/24
# subnet-private-k8s-prd-cluster-a	**********	/26
# subnet-private-k8s-prd-cluster-b	**********	/26
# subnet-private-k8s-prd-a	**********	/20
# subnet-private-k8s-prd-b	**********	/20

resource "aws_subnet" "subnet_public_prd" {
  vpc_id                  = aws_vpc.vpc_workload.id
  cidr_block              = "*********/24"
  availability_zone       = var.aws_zone_b
  map_public_ip_on_launch = true

  tags = {
    Name = "subnet-public-prd"
  }
}

resource "aws_subnet" "subnet_private_vm_prd" {
  vpc_id            = aws_vpc.vpc_workload.id
  cidr_block        = "**********/24"
  availability_zone = var.aws_zone_b

  tags = {
    Name = "subnet-private-vm-prd"
  }
}

resource "aws_subnet" "subnet_private_k8s_prd_cluster_a" {
  vpc_id            = aws_vpc.vpc_workload.id
  cidr_block        = "**********/26"
  availability_zone = var.aws_zone_a

  tags = {
    Name = "subnet-private-k8s-prd-cluster-a"
  }
}

resource "aws_subnet" "subnet_private_k8s_prd_cluster_b" {
  vpc_id            = aws_vpc.vpc_workload.id
  cidr_block        = "**********/26"
  availability_zone = var.aws_zone_b

  tags = {
    Name = "subnet-private-k8s-prd-cluster-b"
  }
}

resource "aws_subnet" "subnet_private_k8s_prd_a" {
  vpc_id            = aws_vpc.vpc_workload.id
  cidr_block        = "**********/20"
  availability_zone = var.aws_zone_a

  tags = {
    Name = "subnet-private-k8s-prd-a"
  }
}

resource "aws_subnet" "subnet_private_k8s_prd_b" {
  vpc_id            = aws_vpc.vpc_workload.id
  cidr_block        = "**********/20"
  availability_zone = var.aws_zone_b

  tags = {
    Name = "subnet-private-k8s-prd-b"
  }
}

# Internet Gateway
resource "aws_internet_gateway" "igw" {
  vpc_id = aws_vpc.vpc_workload.id

  tags = {
    Name = "igw"
  }
}

resource "aws_ec2_transit_gateway_vpc_attachment" "tgw_attachment" {
  transit_gateway_id = data.aws_ec2_transit_gateway.tgw.id
  vpc_id             = aws_vpc.vpc_workload.id
  subnet_ids         = [aws_subnet.subnet_private_k8s_prd_a.id, aws_subnet.subnet_private_k8s_prd_b.id]
  tags               = { Name = "tgw-attachment-workload" }
}

# Route Tables
resource "aws_route_table" "rt_subnet_public_prd" {
  vpc_id = aws_vpc.vpc_workload.id

  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.igw.id
  }

  tags = {
    Name = "rt-subnet-public-prd"
  }
}

resource "aws_route_table" "rt_subnet_private" {
  vpc_id = aws_vpc.vpc_workload.id

  route {
    cidr_block         = "0.0.0.0/0"
    transit_gateway_id = data.aws_ec2_transit_gateway.tgw.id
  }

  tags = {
    Name = "rt-subnet-private"
  }
}

# # Route Table Associations
resource "aws_route_table_association" "rt_subnet_public_prd_association" {
  subnet_id      = aws_subnet.subnet_public_prd.id
  route_table_id = aws_route_table.rt_subnet_public_prd.id
}

resource "aws_route_table_association" "rt_subnet_private_vm_prd_association" {
  subnet_id      = aws_subnet.subnet_private_vm_prd.id
  route_table_id = aws_route_table.rt_subnet_private.id
}

resource "aws_route_table_association" "rt_subnet_private_k8s_prd_cluster_a_association" {
  subnet_id      = aws_subnet.subnet_private_k8s_prd_cluster_a.id
  route_table_id = aws_route_table.rt_subnet_private.id
}

resource "aws_route_table_association" "rt_subnet_private_k8s_prd_cluster_b_association" {
  subnet_id      = aws_subnet.subnet_private_k8s_prd_cluster_b.id
  route_table_id = aws_route_table.rt_subnet_private.id
}

resource "aws_route_table_association" "rt_subnet_private_k8s_prd_a_association" {
  subnet_id      = aws_subnet.subnet_private_k8s_prd_a.id
  route_table_id = aws_route_table.rt_subnet_private.id
}

resource "aws_route_table_association" "rt_subnet_private_k8s_prd_b_association" {
  subnet_id      = aws_subnet.subnet_private_k8s_prd_b.id
  route_table_id = aws_route_table.rt_subnet_private.id
}

