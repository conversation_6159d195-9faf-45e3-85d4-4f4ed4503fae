# Security Group cho VM Lab
resource "aws_security_group" "vm_lab_sg" {
  name        = "vm-lab-sg"
  description = "Security group for VM Lab"
  vpc_id      = aws_vpc.vpc_workload.id

  # SSH access từ VPC
  ingress {
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = [aws_vpc.vpc_workload.cidr_block]
  }

  # HTTP access từ VPC
  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = [aws_vpc.vpc_workload.cidr_block]
  }

  # HTTPS access từ VPC
  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = [aws_vpc.vpc_workload.cidr_block]
  }

  # Outbound traffic
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "vm-lab-sg"
  }
}

resource "aws_instance" "vm_lab" {
  ami                    = "resolve:ssm:/aws/service/ami-amazon-linux-latest/al2023-ami-kernel-default-x86_64"
  instance_type          = "t2.micro"
  subnet_id              = aws_subnet.subnet_private_vm_prd.id
  key_name               = "awslab"
  vpc_security_group_ids = [aws_security_group.vm_lab_sg.id]

  availability_zone = var.aws_zone_b

  user_data = <<-EOF
    #!/bin/bash
    yum update -y
    yum install -y htop curl wget
    echo "VM Lab initialized successfully" > /var/log/vm-lab-init.log
  EOF

  tags = {
    name        = "vm-lab"
    zone        = "zone-b"
    environment = "prd"
  }
}