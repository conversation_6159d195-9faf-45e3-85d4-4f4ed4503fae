graph TB
    subgraph "AWS Account ************ - Networking Project"
        subgraph "VPC Egress (********/16)"
            IGW1[Internet Gateway]
            
            subgraph "AZ: ap-southeast-1b"
                PubSub1[Public Subnet<br/>********/24]
                PrivSub1[Private Subnet<br/>********/24]
                NAT1[NAT Gateway]
                EIP1[Elastic IP]
            end
            
            TGW1[Transit Gateway<br/>Owner: ************]
            
            IGW1 --> PubSub1
            EIP1 --> NAT1
            NAT1 --> PubSub1
            PrivSub1 --> NAT1
            PrivSub1 --> TGW1
        end
    end
    
    subgraph "AWS Account ************ - Workload Project"
        subgraph "VPC Workload (********/16)"
            IGW2[Internet Gateway]
            
            subgraph "AZ: ap-southeast-1a"
                K8sClusterA[K8s Cluster Subnet A<br/>*********/26]
                K8sPodA[K8s Pod Subnet A<br/>*********/20]
            end
            
            subgraph "AZ: ap-southeast-1b"
                PubSub2[Public Subnet<br/>********/24]
                VMSub[VM Private Subnet<br/>*********/24]
                K8sClusterB[K8s Cluster Subnet B<br/>*********/26]
                K8sPodB[K8s Pod Subnet B<br/>*********/20]
            end
            
            IGW2 --> PubSub2
            
            subgraph "Workloads"
                VM[VM Lab<br/>t2.micro<br/>(disabled)]
                EKS[EKS Cluster<br/>eks-prd<br/>(disabled)]
            end
            
            VM --> VMSub
            EKS --> K8sPodA
            EKS --> K8sPodB
        end
    end
    
    subgraph "Internet"
        NET[Internet<br/>0.0.0.0/0]
    end
    
    %% Internet connections
    NET --> IGW1
    NET --> IGW2
    
    %% Transit Gateway connections
    TGW1 -.->|Shared via RAM| TGW2[TGW Attachment<br/>Workload VPC]
    TGW2 --> K8sPodA
    TGW2 --> K8sPodB
    
    %% Cross-VPC routing via TGW
    TGW1 -.->|Routes to| VMSub
    TGW1 -.->|Routes to| K8sClusterA
    TGW1 -.->|Routes to| K8sClusterB
    TGW1 -.->|Routes to| K8sPodA
    TGW1 -.->|Routes to| K8sPodB
    
    %% Route table connections (simplified)
    VMSub -.->|Default route| TGW2
    K8sClusterA -.->|Default route| TGW2
    K8sClusterB -.->|Default route| TGW2
    K8sPodA -.->|Default route| TGW2
    K8sPodB -.->|Default route| TGW2
    
    %% Styling
    classDef vpcBox fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef subnetBox fill:#f3e5f5,stroke:#4a148c,stroke-width:1px
    classDef gwBox fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef workloadBox fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef tgwBox fill:#fce4ec,stroke:#880e4f,stroke-width:3px
    
    class IGW1,IGW2,NAT1 gwBox
    class TGW1,TGW2 tgwBox
    class VM,EKS workloadBox