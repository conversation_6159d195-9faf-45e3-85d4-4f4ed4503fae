# Terraform files
*.tfstate
*.tfstate.*
*.tfvars
*.tfvars.json

# Crash log files
crash.log
crash.*.log

# Exclude all .tfvars files, which are likely to contain sensitive data
*.auto.tfvars
*.auto.tfvars.json

# Include override files you do wish to add to version control
# override.tf
# override.tf.json
# *_override.tf
# *_override.tf.json

# Include tfplan files to ignore the plan output of command: terraform plan -out=tfplan
*tfplan*

# Ignore CLI configuration files
.terraformrc
terraform.rc

# Terraform directories
.terraform/
.terraform.lock.hcl

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Backup files
*.backup
*.bak

# Log files
*.log

# Temporary files
*.tmp
*.temp

# Go files
# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
vendor/

# Go workspace file
go.work
go.work.sum

# Build output
/bin/
/pkg/
/dist/

# Go module cache
go.sum

# Air live reload tool
tmp/

# Delve debugger
__debug_bin

# GoLand IDE
.idea/

# VS Code
.vscode/

# Environment variables
.env
.env.local
.env.*.local

# Application specific
*.db
*.sqlite
*.sqlite3

# Logs
logs/
*.log

# Coverage reports
coverage.txt
coverage.html
coverage.out

# Profiling data
*.prof
*.pprof

# Memory dumps
*.mem

