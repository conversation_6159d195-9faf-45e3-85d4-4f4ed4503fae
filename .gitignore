# Terraform files
*.tfstate
*.tfstate.*
*.tfvars
*.tfvars.json

# Crash log files
crash.log
crash.*.log

# Exclude all .tfvars files, which are likely to contain sensitive data
*.auto.tfvars
*.auto.tfvars.json

# Include override files you do wish to add to version control
# override.tf
# override.tf.json
# *_override.tf
# *_override.tf.json

# Include tfplan files to ignore the plan output of command: terraform plan -out=tfplan
*tfplan*

# Ignore CLI configuration files
.terraformrc
terraform.rc

# Terraform directories
.terraform/
.terraform.lock.hcl

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
Thumbs.db

# Backup files
*.backup
*.bak

# Log files
*.log

# Temporary files
*.tmp
*.temp

