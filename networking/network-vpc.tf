# VPC
resource "aws_vpc" "vpc_egress" {
  cidr_block = "********/16"
  tags = {
    Name = "vpc-egress"
  }
}

# Subnets
resource "aws_subnet" "subnet_egress_private" {
  vpc_id            = aws_vpc.vpc_egress.id
  cidr_block        = "********/24"
  availability_zone = var.aws_zone

  tags = {
    Name = "subnet-egress-private"
  }
}

resource "aws_subnet" "subnet_egress_public" {
  vpc_id                  = aws_vpc.vpc_egress.id
  cidr_block              = "********/24"
  availability_zone       = var.aws_zone
  map_public_ip_on_launch = true

  tags = {
    Name = "subnet-egress-public"
  }
}

# Internet Gateway
resource "aws_internet_gateway" "igw" {
  vpc_id = aws_vpc.vpc_egress.id

  tags = {
    Name = "igw"
  }
}

# Elastic IP for NAT Gateway
resource "aws_eip" "ip_nat_gw" {
  domain = "vpc"

  tags = {
    Name = "ip-nat-gw"
  }
}

# NAT Gateway
resource "aws_nat_gateway" "nat_gw" {
  allocation_id = aws_eip.ip_nat_gw.id
  subnet_id     = aws_subnet.subnet_egress_public.id

  tags = {
    Name = "nat-gw"
  }
}

# Transit Gateway
module "tgw" {
  source  = "terraform-aws-modules/transit-gateway/aws"
  version = "~> 2.0"

  name        = "tgw"
  description = "TGW shared with several other AWS accounts"

  enable_auto_accept_shared_attachments  = true
  enable_default_route_table_association = true
  enable_default_route_table_propagation = true
  create_tgw_routes                      = false

  vpc_attachments = {
    vpc = {
      vpc_id                                          = aws_vpc.vpc_egress.id
      subnet_ids                                      = [aws_subnet.subnet_egress_private.id]
      dns_support                                     = true
      transit_gateway_default_route_table_association = true
      transit_gateway_default_route_table_propagation = true
    }
  }

  ram_allow_external_principals = true
  ram_principals                = [************]

}

# Route Tables
resource "aws_route_table" "rt_subnet_egress_public" {
  vpc_id = aws_vpc.vpc_egress.id

  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.igw.id
  }

  route {
    cidr_block         = "*********/24"
    transit_gateway_id = module.tgw.ec2_transit_gateway_id
  }

  route {
    cidr_block         = "*********/26"
    transit_gateway_id = module.tgw.ec2_transit_gateway_id
  }

  route {
    cidr_block         = "*********/26"
    transit_gateway_id = module.tgw.ec2_transit_gateway_id
  }

  route {
    cidr_block         = "*********/20"
    transit_gateway_id = module.tgw.ec2_transit_gateway_id
  }

  route {
    cidr_block         = "*********/20"
    transit_gateway_id = module.tgw.ec2_transit_gateway_id
  }

  tags = {
    Name = "rt-subnet-egress-public"
  }
}

resource "aws_route_table" "rt_subnet_egress_private" {
  vpc_id = aws_vpc.vpc_egress.id

  route {
    cidr_block     = "0.0.0.0/0"
    nat_gateway_id = aws_nat_gateway.nat_gw.id
  }

  tags = {
    Name = "rt-subnet-egress-private"
  }
}

# Route Table Associations
resource "aws_route_table_association" "rt_subnet_egress_public_association" {
  subnet_id      = aws_subnet.subnet_egress_public.id
  route_table_id = aws_route_table.rt_subnet_egress_public.id
}

resource "aws_route_table_association" "rt_subnet_egress_private_association" {
  subnet_id      = aws_subnet.subnet_egress_private.id
  route_table_id = aws_route_table.rt_subnet_egress_private.id
}

