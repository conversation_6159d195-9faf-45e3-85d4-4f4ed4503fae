# ------------ VPC
resource "aws_vpc" "vpc_egress" {
  cidr_block = "********/16"
  tags = {
    Name = "vpc-egress"
  }
}

# ------------ Subnets
resource "aws_subnet" "subnet_egress_private" {
  vpc_id            = aws_vpc.vpc_egress.id
  cidr_block        = "********/24"
  availability_zone = var.aws_zone

  tags = {
    Name = "subnet-egress-private"
  }
}

resource "aws_subnet" "subnet_egress_public" {
  vpc_id                  = aws_vpc.vpc_egress.id
  cidr_block              = "********/24"
  availability_zone       = var.aws_zone
  map_public_ip_on_launch = true

  tags = {
    Name = "subnet-egress-public"
  }
}

# ------------ Internet Gateway
resource "aws_internet_gateway" "igw" {
  vpc_id = aws_vpc.vpc_egress.id

  tags = {
    Name = "igw"
  }
}

# ------------ Elastic IP for NAT Gateway
resource "aws_eip" "ip_nat_gw" {
  domain = "vpc"

  tags = {
    Name = "ip-nat-gw"
  }
}

# ------------ NAT Gateway
resource "aws_nat_gateway" "nat_gw" {
  allocation_id = aws_eip.ip_nat_gw.id
  subnet_id     = aws_subnet.subnet_egress_public.id

  tags = {
    Name = "nat-gw"
  }
}

# ------------ Transit Gateway
resource "aws_ec2_transit_gateway" "transit_gateway" {
  amazon_side_asn                 = 64513
  auto_accept_shared_attachments  = "enable"
  default_route_table_association = "enable"
  default_route_table_propagation = "enable"
  description                     = "TGW shared with several other AWS accounts"
  tags = {
    Name = "tgw"
  }
}

resource "aws_ec2_transit_gateway_vpc_attachment" "transit_gateway_vpc_attachment" {
  subnet_ids         = [aws_subnet.subnet_egress_private.id]
  transit_gateway_id = aws_ec2_transit_gateway.transit_gateway.id
  vpc_id             = aws_vpc.vpc_egress.id
}

resource "aws_ec2_transit_gateway_route" "transit_gateway_route" {
  destination_cidr_block         = "0.0.0.0/0"
  transit_gateway_attachment_id  = aws_ec2_transit_gateway_vpc_attachment.transit_gateway_vpc_attachment.id
  transit_gateway_route_table_id = aws_ec2_transit_gateway.transit_gateway.association_default_route_table_id
}

resource "aws_ec2_transit_gateway_route_table" "tgw_route_table_infras" {
  transit_gateway_id = aws_ec2_transit_gateway.transit_gateway.id
  tags = {
    Name = "tgw-rt-infras"
  }
}

# ------------ RAM ram_resource_share
resource "aws_ram_resource_share" "ram_resource_share" {
  name                      = "tgw"
  allow_external_principals = true

  tags = {
    Environment = "Production"
  }
}

resource "aws_ram_resource_association" "example" {
  resource_arn       = aws_ec2_transit_gateway.transit_gateway.arn
  resource_share_arn = aws_ram_resource_share.ram_resource_share.arn
}

resource "aws_ram_principal_association" "ram_principal_association_workload" {
  principal          = "669926049955"
  resource_share_arn = aws_ram_resource_share.ram_resource_share.arn
}

resource "aws_ram_principal_association" "ram_principal_association_infras" {
  principal          = "263303066483"
  resource_share_arn = aws_ram_resource_share.ram_resource_share.arn
}

# ------------ Route Tables
resource "aws_route_table" "rt_subnet_egress_public" {
  vpc_id = aws_vpc.vpc_egress.id

  route {
    cidr_block = "0.0.0.0/0"
    gateway_id = aws_internet_gateway.igw.id
  }

  route {
    cidr_block         = "**********/24"
    transit_gateway_id = aws_ec2_transit_gateway.transit_gateway.id
  }

  route {
    cidr_block         = "**********/26"
    transit_gateway_id = aws_ec2_transit_gateway.transit_gateway.id
  }

  route {
    cidr_block         = "**********/26"
    transit_gateway_id = aws_ec2_transit_gateway.transit_gateway.id
  }

  route {
    cidr_block         = "**********/20"
    transit_gateway_id = aws_ec2_transit_gateway.transit_gateway.id
  }

  route {
    cidr_block         = "**********/20"
    transit_gateway_id = aws_ec2_transit_gateway.transit_gateway.id
  }

  tags = {
    Name = "rt-subnet-egress-public"
  }
}

resource "aws_route_table" "rt_subnet_egress_private" {
  vpc_id = aws_vpc.vpc_egress.id

  route {
    cidr_block     = "0.0.0.0/0"
    nat_gateway_id = aws_nat_gateway.nat_gw.id
  }

  tags = {
    Name = "rt-subnet-egress-private"
  }
}

# ------------ Route Table Associations
resource "aws_route_table_association" "rt_subnet_egress_public_association" {
  subnet_id      = aws_subnet.subnet_egress_public.id
  route_table_id = aws_route_table.rt_subnet_egress_public.id
}

resource "aws_route_table_association" "rt_subnet_egress_private_association" {
  subnet_id      = aws_subnet.subnet_egress_private.id
  route_table_id = aws_route_table.rt_subnet_egress_private.id
}

